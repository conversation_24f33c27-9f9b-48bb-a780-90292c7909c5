import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';

const HeroSection = () => (
  <section className='relative w-full min-h-[500px] md:min-h-[480px] flex flex-col justify-end mb-16 md:mb-0'>
    {/* Vertical Pagination Dots */}
    <div className='hero-swiper-pagination absolute right-4 md:right-8 top-1/2 -translate-y-1/2 flex flex-col gap-2 md:gap-4 z-20' />
    {/* Swiper Carousel */}
    <Swiper
      modules={[Pagination]}
      pagination={{ clickable: true, el: '.hero-swiper-pagination' }}
      className='w-full h-[500px] md:h-[480px]'
    >
      {/* Slide 1 */}
      <SwiperSlide>
        <div
          className='relative w-full h-[500px] md:h-[480px] bg-cover bg-center'
          style={{
            backgroundImage:
              'url(https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1440&q=80)',
          }}
        >
          <div className='absolute inset-0 bg-black/50 z-0' />
          <div className='relative z-10 max-w-screen-xl mx-auto px-4 md:px-6 flex flex-row items-center h-full'>
            <div className='w-full md:w-1/2 flex flex-col justify-center items-center md:items-start h-full text-center md:text-left pb-32 md:pb-0'>
              <span className='text-white text-xs md:text-sm font-semibold mb-1 md:mb-2 block'>
                CapsuleTransit MAX
              </span>
              <h1 className='text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold text-white mb-3 md:mb-4 max-w-2xl leading-tight'>
                Premium stay experience you need at KLIA
              </h1>
              <span className='text-white text-sm md:text-base font-medium mb-6 md:mb-8 block'>
                KLIA Terminal 2 | Public Area
              </span>
            </div>
            <div className='hidden md:block md:w-1/2' />
          </div>
        </div>
      </SwiperSlide>
      {/* Slide 2 */}
      <SwiperSlide>
        <div
          className='relative w-full h-[500px] md:h-[480px] bg-cover bg-center'
          style={{
            backgroundImage:
              'url(https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=1440&q=80)',
          }}
        >
          <div className='absolute inset-0 bg-black/40 z-0' />
          <div className='relative z-10 max-w-screen-xl mx-auto px-4 md:px-6 flex flex-row items-center h-full'>
            <div className='w-full md:w-1/2 flex flex-col justify-center items-center md:items-start h-full text-center md:text-left pb-32 md:pb-0'>
              <span className='text-white text-xs md:text-sm font-semibold mb-1 md:mb-2 block'>
                CapsuleTransit MAX
              </span>
              <h1 className='text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold text-white mb-3 md:mb-4 max-w-2xl leading-tight'>
                Rest, Refresh, and Recharge
              </h1>
              <span className='text-white text-sm md:text-base font-medium mb-6 md:mb-8 block'>
                KLIA Terminal 2 | En-suite Rooms
              </span>
            </div>
            <div className='hidden md:block md:w-1/2' />
          </div>
        </div>
      </SwiperSlide>
      {/* Slide 3 */}
      <SwiperSlide>
        <div
          className='relative w-full h-[500px] md:h-[480px] bg-cover bg-center'
          style={{
            backgroundImage:
              'url(https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=1440&q=80)',
          }}
        >
          <div className='absolute inset-0 bg-black/40 z-0' />
          <div className='relative z-10 max-w-screen-xl mx-auto px-4 md:px-6 flex flex-row items-center h-full'>
            <div className='w-full md:w-1/2 flex flex-col justify-center items-center md:items-start h-full text-center md:text-left pb-32 md:pb-0'>
              <span className='text-white text-xs md:text-sm font-semibold mb-1 md:mb-2 block'>
                CapsuleTransit MAX
              </span>
              <h1 className='text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold text-white mb-3 md:mb-4 max-w-2xl leading-tight'>
                Modern Comfort at the Airport
              </h1>
              <span className='text-white text-sm md:text-base font-medium mb-6 md:mb-8 block'>
                KLIA Terminal 2 | 24/7 Service
              </span>
            </div>
            <div className='hidden md:block md:w-1/2' />
          </div>
        </div>
      </SwiperSlide>
      {/* Slide 4 */}
      <SwiperSlide>
        <div
          className='relative w-full h-[500px] md:h-[480px] bg-cover bg-center'
          style={{
            backgroundImage:
              'url(https://images.unsplash.com/photo-1512918728675-ed5a9ecdebfd?auto=format&fit=crop&w=1440&q=80)',
          }}
        >
          <div className='absolute inset-0 bg-black/40 z-0' />
          <div className='relative z-10 max-w-screen-xl mx-auto px-4 md:px-6 flex flex-row items-center h-full'>
            <div className='w-full md:w-1/2 flex flex-col justify-center items-center md:items-start h-full text-center md:text-left pb-32 md:pb-0'>
              <span className='text-white text-xs md:text-sm font-semibold mb-1 md:mb-2 block'>
                CapsuleTransit MAX
              </span>
              <h1 className='text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold text-white mb-3 md:mb-4 max-w-2xl leading-tight'>
                Award-winning Capsule Hotel
              </h1>
              <span className='text-white text-sm md:text-base font-medium mb-6 md:mb-8 block'>
                KLIA Terminal 2 | Awarded & Trusted
              </span>
            </div>
            <div className='hidden md:block md:w-1/2' />
          </div>
        </div>
      </SwiperSlide>
    </Swiper>
    {/* Booking Card overlays the carousel */}
    <div className='absolute left-1/2 -translate-x-1/2 bottom-0 translate-y-1/2 w-full max-w-4xl z-30 px-4 md:px-0'>
      <div className='bg-white rounded-xl shadow-lg p-4 md:p-6 lg:p-8 flex flex-col gap-0 border-b-4 border-[#a259ff] relative'>
        {/* OUTLET row */}
        <div className='flex flex-col md:flex-row items-start md:items-center justify-between gap-1 md:gap-2 mb-2'>
          <label className='block text-xs font-semibold mb-1 text-gray-700 w-full md:w-auto'>
            OUTLET
          </label>
          <a
            href='#'
            className='text-xs text-blue-700 hover:underline self-start md:ml-auto md:self-auto md:order-2'
          >
            Learn about our outlets
          </a>
        </div>
        <div className='mb-3 md:mb-4'>
          <select className='w-full border border-gray-300 rounded px-3 md:px-4 py-2.5 md:py-3 text-sm md:text-base placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#a259ff] transition'>
            <option value='' disabled selected hidden>
              Select the outlet you want to stay
            </option>
            <option>CapsuleTransit MAX (Terminal 2, Public Area)</option>
            <option>
              CapsuleTransit Landside (Terminal 2, Public Area)
            </option>
            <option>
              CapsuleTransit Airside (Terminal 2, Restricted Area)
            </option>
          </select>
        </div>
        <div className='w-full h-px bg-gray-200 mb-2' />
        <div className='flex flex-col lg:flex-row items-stretch lg:items-end gap-3 md:gap-4 lg:gap-6'>
          {/* DATE */}
          <div className='flex-1 min-w-0'>
            <label className='block text-xs font-semibold mb-1 text-gray-700'>
              DATE
            </label>
            <div className='relative'>
              <select className='w-full border border-gray-300 rounded px-3 md:px-4 py-2.5 md:py-3 text-sm md:text-base appearance-none focus:outline-none focus:ring-2 focus:ring-[#a259ff] transition'>
                <option>27 May 2025 (Tue)</option>
              </select>
              <span className='absolute right-2 md:right-3 top-1/2 -translate-y-1/2 pointer-events-none text-gray-400 text-sm'>
                ▼
              </span>
            </div>
          </div>
          {/* CHECK-IN TIME */}
          <div className='flex-1 min-w-0'>
            <label className='block text-xs font-semibold mb-1 text-gray-700'>
              CHECK-IN TIME
            </label>
            <div className='relative'>
              <select className='w-full border border-gray-300 rounded px-3 md:px-4 py-2.5 md:py-3 text-sm md:text-base appearance-none focus:outline-none focus:ring-2 focus:ring-[#a259ff] transition'>
                <option>3:00 PM</option>
              </select>
              <span className='absolute right-2 md:right-3 top-1/2 -translate-y-1/2 pointer-events-none text-gray-400 text-sm'>
                ▼
              </span>
            </div>
          </div>
          {/* STAY DURATION */}
          <div className='flex-1 min-w-0'>
            <label className='block text-xs font-semibold mb-1 text-gray-700'>
              STAY DURATION
            </label>
            <div className='relative'>
              <select
                disabled
                className='w-full border border-gray-200 bg-gray-100 text-gray-400 rounded px-3 md:px-4 py-2.5 md:py-3 text-sm md:text-base appearance-none cursor-not-allowed'
              >
                <option>Please select the outlet first</option>
              </select>
              <span className='absolute right-2 md:right-3 top-1/2 -translate-y-1/2 pointer-events-none text-gray-300 text-sm'>
                ▼
              </span>
            </div>
          </div>
          {/* CHECK AVAILABILITY BUTTON */}
          <div className='flex items-end w-full lg:w-auto mt-2 lg:mt-0'>
            <button className='bg-[#011589] text-white font-bold px-6 md:px-8 py-2.5 md:py-3 rounded-lg shadow hover:bg-blue-900 transition w-full lg:w-auto text-sm md:text-base whitespace-nowrap'>
              CHECK AVAILABILITY
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default HeroSection; 