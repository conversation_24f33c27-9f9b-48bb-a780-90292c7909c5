import React, { useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';

const OutletsSection = () => {
  const [expandedCard, setExpandedCard] = useState<number | null>(null);

  const toggleCard = (cardIndex: number) => {
    setExpandedCard(expandedCard === cardIndex ? null : cardIndex);
  };

  return (
    <section className='w-full bg-[#f5f7fa] py-16 px-4'>
      <div className='max-w-6xl mx-auto'>
        <h2 className='text-2xl md:text-3xl font-bold text-center mb-10 underline'>
          Our Outlets and Location
        </h2>
        <div className='flex flex-col gap-8'>
          {/* Outlet 1 - CapsuleTransit MAX */}
          <div className='bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden relative'>
            {/* NEW OPENING Badge */}
            <div className='absolute right-0 top-0 z-20'>
              <div className='bg-[#223a8a] text-white text-xs font-bold px-4 py-2 rounded-bl-2xl rounded-tr-2xl shadow-lg transform rotate-45 origin-bottom-left translate-x-4 -translate-y-4'>
                <span className='transform block whitespace-nowrap'>NEW OPENING</span>
              </div>
            </div>

            {/* Desktop Layout */}
            <div className='hidden md:flex'>
              {/* Image Carousel */}
              <div className='relative w-1/3'>
                <Swiper
                  modules={[Navigation]}
                  navigation={{
                    nextEl: '.swiper-button-next-1',
                    prevEl: '.swiper-button-prev-1',
                  }}
                  className='h-full'
                >
                  <SwiperSlide>
                    <img
                      src='https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80'
                      alt='CapsuleTransit MAX Room 1'
                      className='w-full h-full object-cover'
                    />
                  </SwiperSlide>
                  <SwiperSlide>
                    <img
                      src='https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=400&q=80'
                      alt='CapsuleTransit MAX Room 2'
                      className='w-full h-full object-cover'
                    />
                  </SwiperSlide>
                  <SwiperSlide>
                    <img
                      src='https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=400&q=80'
                      alt='CapsuleTransit MAX Room 3'
                      className='w-full h-full object-cover'
                    />
                  </SwiperSlide>
                </Swiper>
                {/* Navigation Arrows */}
                <button className='swiper-button-prev-1 absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all'>
                  <svg className='w-4 h-4 text-gray-700' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M15 19l-7-7 7-7' />
                  </svg>
                </button>
                <button className='swiper-button-next-1 absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all'>
                  <svg className='w-4 h-4 text-gray-700' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M9 5l7 7-7 7' />
                  </svg>
                </button>
              </div>

              {/* Content */}
              <div className='flex-1 p-6'>
                <div className='flex items-center gap-2 mb-3'>
                  <span className='bg-[#e8f5e8] text-[#223a8a] font-semibold text-xs px-3 py-1 rounded-full'>
                    Terminal 2
                  </span>
                  <span className='bg-[#e8f5e8] text-[#223a8a] font-semibold text-xs px-3 py-1 rounded-full'>
                    Public Area
                  </span>
                </div>
                <h3 className='text-2xl font-bold mb-2 text-gray-900'>CapsuleTransit MAX</h3>
                <p className='text-gray-600 mb-4 leading-relaxed'>
                  Premium stay in the public area of Terminal 2. Privacy, Space, En-suite bathroom.
                </p>

                <div className='grid grid-cols-3 gap-6'>
                  <div>
                    <div className='text-xs font-bold text-gray-500 mb-2 uppercase tracking-wide'>
                      ROOM TYPE
                    </div>
                    <div className='space-y-1'>
                      <div className='text-sm font-medium'>Deluxe</div>
                      <div className='text-xs text-gray-500'>Twin/Queen/King Bed for 2 Adults</div>
                      <div className='text-sm font-medium'>Executive</div>
                      <div className='text-xs text-gray-500'>King Bed for 2 Adults</div>
                      <div className='text-sm font-medium'>Runway Suite</div>
                      <div className='text-xs text-gray-500'>King Bed with Sofa for 2 Adults</div>
                    </div>
                  </div>
                  <div>
                    <div className='text-xs font-bold text-gray-500 mb-2 uppercase tracking-wide'>
                      From
                    </div>
                    <div className='space-y-1'>
                      <div className='text-sm font-bold text-gray-900'>RM200</div>
                      <div className='text-sm font-bold text-gray-900 mt-6'>RM400</div>
                      <div className='text-sm font-bold text-gray-900 mt-6'>RM600</div>
                    </div>
                  </div>
                  <div>
                    <div className='text-xs font-bold text-gray-500 mb-2 uppercase tracking-wide'>
                      FACILITIES
                    </div>
                    <div className='grid grid-cols-2 gap-2 text-xs'>
                      <div className='flex items-center gap-1'>
                        <span>🗄️</span> Lockers
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>📶</span> Wifi
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>🛁</span> En-suite Bathroom & Towel
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>🧴</span> Toiletries
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>🥿</span> Slippers
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>☕</span> Water & Coffee
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className='flex flex-col justify-center items-center bg-gray-50 px-6 py-8 min-w-[160px] border-l border-gray-200'>
                <button className='bg-white border-2 border-[#223a8a] text-[#223a8a] font-bold px-6 py-3 rounded-lg mb-3 hover:bg-[#223a8a] hover:text-white transition-all duration-200 w-full'>
                  BOOK NOW
                </button>
                <a href='#' className='text-[#223a8a] text-sm font-medium underline hover:no-underline'>
                  View Outlet
                </a>
              </div>
            </div>

            {/* Mobile Layout */}
            <div className='md:hidden'>
              {/* Image Carousel */}
              <div className='relative h-64'>
                <Swiper
                  modules={[Navigation]}
                  navigation={{
                    nextEl: '.swiper-button-next-mobile-1',
                    prevEl: '.swiper-button-prev-mobile-1',
                  }}
                  className='h-full'
                >
                  <SwiperSlide>
                    <img
                      src='https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80'
                      alt='CapsuleTransit MAX Room 1'
                      className='w-full h-full object-cover'
                    />
                  </SwiperSlide>
                  <SwiperSlide>
                    <img
                      src='https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=400&q=80'
                      alt='CapsuleTransit MAX Room 2'
                      className='w-full h-full object-cover'
                    />
                  </SwiperSlide>
                  <SwiperSlide>
                    <img
                      src='https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=400&q=80'
                      alt='CapsuleTransit MAX Room 3'
                      className='w-full h-full object-cover'
                    />
                  </SwiperSlide>
                </Swiper>
                {/* Navigation Arrows */}
                <button className='swiper-button-prev-mobile-1 absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all'>
                  <svg className='w-4 h-4 text-gray-700' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M15 19l-7-7 7-7' />
                  </svg>
                </button>
                <button className='swiper-button-next-mobile-1 absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all'>
                  <svg className='w-4 h-4 text-gray-700' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M9 5l7 7-7 7' />
                  </svg>
                </button>
                {/* Pagination Dots */}
                <div className='absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2'>
                  <div className='w-2 h-2 bg-white rounded-full opacity-50'></div>
                  <div className='w-2 h-2 bg-white rounded-full'></div>
                  <div className='w-2 h-2 bg-white rounded-full opacity-50'></div>
                </div>
              </div>

              {/* Content */}
              <div className='p-4'>
                <div className='flex items-center gap-2 mb-3'>
                  <span className='bg-[#e8f5e8] text-[#223a8a] font-semibold text-xs px-3 py-1 rounded-full'>
                    Terminal 2
                  </span>
                  <span className='bg-[#e8f5e8] text-[#223a8a] font-semibold text-xs px-3 py-1 rounded-full'>
                    Public Area
                  </span>
                </div>
                <h3 className='text-xl font-bold mb-2 text-gray-900'>CapsuleTransit MAX</h3>
                <p className='text-gray-600 mb-4 text-sm leading-relaxed'>
                  Premium stay in the public area of Terminal 2. Privacy, Space, En-suite bathroom.
                </p>

                {/* Room Types - Always Visible */}
                <div className='space-y-3 mb-4'>
                  <div className='text-xs font-bold text-gray-500 uppercase tracking-wide'>
                    ROOM TYPE
                  </div>
                  <div className='space-y-2'>
                    <div className='flex justify-between items-center'>
                      <div>
                        <div className='text-sm font-medium'>Deluxe</div>
                        <div className='text-xs text-gray-500'>Twin/Queen/King Bed for 2 Adults</div>
                      </div>
                      <div className='text-sm font-bold text-gray-900'>RM200</div>
                    </div>
                    <div className='flex justify-between items-center'>
                      <div>
                        <div className='text-sm font-medium'>Executive</div>
                        <div className='text-xs text-gray-500'>King Bed for 2 Adults</div>
                      </div>
                      <div className='text-sm font-bold text-gray-900'>RM400</div>
                    </div>
                    <div className='flex justify-between items-center'>
                      <div>
                        <div className='text-sm font-medium'>Runway Suite</div>
                        <div className='text-xs text-gray-500'>King Bed with Sofa for 2 Adults</div>
                      </div>
                      <div className='text-sm font-bold text-gray-900'>RM600</div>
                    </div>
                  </div>
                </div>

                {/* Expandable Content */}
                <div className={`transition-all duration-300 overflow-hidden ${expandedCard === 0 ? 'max-h-96' : 'max-h-0'}`}>
                  <div className='pt-4 border-t border-gray-200'>
                    <div className='text-xs font-bold text-gray-500 mb-3 uppercase tracking-wide'>
                      FACILITIES
                    </div>
                    <div className='grid grid-cols-2 gap-2 text-xs mb-4'>
                      <div className='flex items-center gap-1'>
                        <span>🗄️</span> Lockers
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>📶</span> Wifi
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>🛁</span> En-suite Bathroom & Towel
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>🧴</span> Toiletries
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>🥿</span> Slippers
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>☕</span> Water & Coffee
                      </div>
                    </div>
                  </div>
                </div>

                {/* Expand/Collapse Button */}
                <button
                  onClick={() => toggleCard(0)}
                  className='w-full flex items-center justify-center py-2 text-[#223a8a] font-medium text-sm'
                >
                  <svg
                    className={`w-4 h-4 transition-transform duration-200 ${expandedCard === 0 ? 'rotate-180' : ''}`}
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M19 9l-7 7-7-7' />
                  </svg>
                </button>

                {/* Action Buttons */}
                <div className='flex flex-col gap-2 mt-4'>
                  <button className='bg-white border-2 border-[#223a8a] text-[#223a8a] font-bold px-6 py-3 rounded-lg hover:bg-[#223a8a] hover:text-white transition-all duration-200 w-full'>
                    BOOK NOW
                  </button>
                  <a href='#' className='text-[#223a8a] text-sm font-medium underline hover:no-underline text-center'>
                    View Outlet
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Outlet 2 - CapsuleTransit Landside */}
          <div className='bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden relative'>
            {/* Desktop Layout */}
            <div className='hidden md:flex'>
              {/* Image Carousel */}
              <div className='relative w-1/3'>
                <Swiper
                  modules={[Navigation]}
                  navigation={{
                    nextEl: '.swiper-button-next-2',
                    prevEl: '.swiper-button-prev-2',
                  }}
                  className='h-full'
                >
                  <SwiperSlide>
                    <img
                      src='https://images.unsplash.com/photo-1512918728675-ed5a9ecdebfd?auto=format&fit=crop&w=400&q=80'
                      alt='CapsuleTransit Landside Room 1'
                      className='w-full h-full object-cover'
                    />
                  </SwiperSlide>
                  <SwiperSlide>
                    <img
                      src='https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=400&q=80'
                      alt='CapsuleTransit Landside Room 2'
                      className='w-full h-full object-cover'
                    />
                  </SwiperSlide>
                </Swiper>
                {/* Navigation Arrows */}
                <button className='swiper-button-prev-2 absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all'>
                  <svg className='w-4 h-4 text-gray-700' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M15 19l-7-7 7-7' />
                  </svg>
                </button>
                <button className='swiper-button-next-2 absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all'>
                  <svg className='w-4 h-4 text-gray-700' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M9 5l7 7-7 7' />
                  </svg>
                </button>
              </div>

              {/* Content */}
              <div className='flex-1 p-6'>
                <div className='flex items-center gap-2 mb-3'>
                  <span className='bg-[#e8f5e8] text-[#223a8a] font-semibold text-xs px-3 py-1 rounded-full'>
                    Terminal 2
                  </span>
                  <span className='bg-[#e8f5e8] text-[#223a8a] font-semibold text-xs px-3 py-1 rounded-full'>
                    Public Area
                  </span>
                </div>
                <h3 className='text-2xl font-bold mb-2 text-gray-900'>CapsuleTransit Landside</h3>
                <p className='text-gray-600 mb-4 leading-relaxed'>
                  Capsule hotel with showers and lockers, located in the public area.
                </p>

                <div className='grid grid-cols-3 gap-6'>
                  <div>
                    <div className='text-xs font-bold text-gray-500 mb-2 uppercase tracking-wide'>
                      ROOM TYPE
                    </div>
                    <div className='space-y-1'>
                      <div className='text-sm font-medium'>Single Capsule</div>
                      <div className='text-xs text-gray-500'>Single Capsule for 1 Adult</div>
                      <div className='text-sm font-medium'>Queen Capsule</div>
                      <div className='text-xs text-gray-500'>Queen Capsule for 1 Adult</div>
                      <div className='text-sm font-medium'>Private Capsule Suite</div>
                      <div className='text-xs text-gray-500'>Private Capsule for 1 Adult</div>
                    </div>
                  </div>
                  <div>
                    <div className='text-xs font-bold text-gray-500 mb-2 uppercase tracking-wide'>
                      From
                    </div>
                    <div className='space-y-1'>
                      <div className='text-sm font-bold text-gray-900'>RM200</div>
                      <div className='text-sm font-bold text-gray-900 mt-6'>RM400</div>
                      <div className='text-sm font-bold text-gray-900 mt-6'>RM600</div>
                    </div>
                  </div>
                  <div>
                    <div className='text-xs font-bold text-gray-500 mb-2 uppercase tracking-wide'>
                      FACILITIES
                    </div>
                    <div className='grid grid-cols-2 gap-2 text-xs'>
                      <div className='flex items-center gap-1'>
                        <span>🗄️</span> Lockers
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>📶</span> Wifi
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>🛁</span> Shared Bathroom & Towel
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>🧴</span> Toiletries
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>🥿</span> Slippers
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>☕</span> Water & Coffee
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className='flex flex-col justify-center items-center bg-gray-50 px-6 py-8 min-w-[160px] border-l border-gray-200'>
                <button className='bg-white border-2 border-[#223a8a] text-[#223a8a] font-bold px-6 py-3 rounded-lg mb-3 hover:bg-[#223a8a] hover:text-white transition-all duration-200 w-full'>
                  BOOK NOW
                </button>
                <a href='#' className='text-[#223a8a] text-sm font-medium underline hover:no-underline'>
                  View Outlet
                </a>
              </div>
            </div>

            {/* Mobile Layout */}
            <div className='md:hidden'>
              {/* Image Carousel */}
              <div className='relative h-64'>
                <Swiper
                  modules={[Navigation]}
                  navigation={{
                    nextEl: '.swiper-button-next-mobile-2',
                    prevEl: '.swiper-button-prev-mobile-2',
                  }}
                  className='h-full'
                >
                  <SwiperSlide>
                    <img
                      src='https://images.unsplash.com/photo-1512918728675-ed5a9ecdebfd?auto=format&fit=crop&w=400&q=80'
                      alt='CapsuleTransit Landside Room 1'
                      className='w-full h-full object-cover'
                    />
                  </SwiperSlide>
                  <SwiperSlide>
                    <img
                      src='https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=400&q=80'
                      alt='CapsuleTransit Landside Room 2'
                      className='w-full h-full object-cover'
                    />
                  </SwiperSlide>
                </Swiper>
                {/* Navigation Arrows */}
                <button className='swiper-button-prev-mobile-2 absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all'>
                  <svg className='w-4 h-4 text-gray-700' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M15 19l-7-7 7-7' />
                  </svg>
                </button>
                <button className='swiper-button-next-mobile-2 absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all'>
                  <svg className='w-4 h-4 text-gray-700' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M9 5l7 7-7 7' />
                  </svg>
                </button>
                {/* Pagination Dots */}
                <div className='absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2'>
                  <div className='w-2 h-2 bg-white rounded-full'></div>
                  <div className='w-2 h-2 bg-white rounded-full opacity-50'></div>
                </div>
              </div>

              {/* Content */}
              <div className='p-4'>
                <div className='flex items-center gap-2 mb-3'>
                  <span className='bg-[#e8f5e8] text-[#223a8a] font-semibold text-xs px-3 py-1 rounded-full'>
                    Terminal 2
                  </span>
                  <span className='bg-[#e8f5e8] text-[#223a8a] font-semibold text-xs px-3 py-1 rounded-full'>
                    Public Area
                  </span>
                </div>
                <h3 className='text-xl font-bold mb-2 text-gray-900'>CapsuleTransit Landside</h3>
                <p className='text-gray-600 mb-4 text-sm leading-relaxed'>
                  Capsule hotel with showers and lockers, located in the public area.
                </p>

                {/* Room Types - Always Visible */}
                <div className='space-y-3 mb-4'>
                  <div className='text-xs font-bold text-gray-500 uppercase tracking-wide'>
                    ROOM TYPE
                  </div>
                  <div className='space-y-2'>
                    <div className='flex justify-between items-center'>
                      <div>
                        <div className='text-sm font-medium'>Single Capsule</div>
                        <div className='text-xs text-gray-500'>Single Capsule for 1 Adult</div>
                      </div>
                      <div className='text-sm font-bold text-gray-900'>RM200</div>
                    </div>
                    <div className='flex justify-between items-center'>
                      <div>
                        <div className='text-sm font-medium'>Queen Capsule</div>
                        <div className='text-xs text-gray-500'>Queen Capsule for 1 Adult</div>
                      </div>
                      <div className='text-sm font-bold text-gray-900'>RM400</div>
                    </div>
                    <div className='flex justify-between items-center'>
                      <div>
                        <div className='text-sm font-medium'>Private Capsule Suite</div>
                        <div className='text-xs text-gray-500'>Private Capsule for 1 Adult</div>
                      </div>
                      <div className='text-sm font-bold text-gray-900'>RM600</div>
                    </div>
                  </div>
                </div>

                {/* Expandable Content */}
                <div className={`transition-all duration-300 overflow-hidden ${expandedCard === 1 ? 'max-h-96' : 'max-h-0'}`}>
                  <div className='pt-4 border-t border-gray-200'>
                    <div className='text-xs font-bold text-gray-500 mb-3 uppercase tracking-wide'>
                      FACILITIES
                    </div>
                    <div className='grid grid-cols-2 gap-2 text-xs mb-4'>
                      <div className='flex items-center gap-1'>
                        <span>🗄️</span> Lockers
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>📶</span> Wifi
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>🛁</span> Shared Bathroom & Towel
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>🧴</span> Toiletries
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>🥿</span> Slippers
                      </div>
                      <div className='flex items-center gap-1'>
                        <span>☕</span> Water & Coffee
                      </div>
                    </div>
                  </div>
                </div>

                {/* Expand/Collapse Button */}
                <button
                  onClick={() => toggleCard(1)}
                  className='w-full flex items-center justify-center py-2 text-[#223a8a] font-medium text-sm'
                >
                  <svg
                    className={`w-4 h-4 transition-transform duration-200 ${expandedCard === 1 ? 'rotate-180' : ''}`}
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M19 9l-7 7-7-7' />
                  </svg>
                </button>

                {/* Action Buttons */}
                <div className='flex flex-col gap-2 mt-4'>
                  <button className='bg-white border-2 border-[#223a8a] text-[#223a8a] font-bold px-6 py-3 rounded-lg hover:bg-[#223a8a] hover:text-white transition-all duration-200 w-full'>
                    BOOK NOW
                  </button>
                  <a href='#' className='text-[#223a8a] text-sm font-medium underline hover:no-underline text-center'>
                    View Outlet
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OutletsSection;