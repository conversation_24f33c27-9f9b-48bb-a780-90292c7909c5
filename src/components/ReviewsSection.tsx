import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Navigation } from 'swiper/modules';

const reviews = [
  {
    name: 'Waz',
    country: 'Mauritius',
    flag: '🇲🇺',
    title: 'Convenient and Comfortable Stay at Capsule Transit',
    content:
      'Capsule Transit was definitely worth it—affordable and fairly comfortable. The showers and toilets were exceptionally clean, and the staff were friendly and helpful.',
    link: 'https://www.tripadvisor.com/',
    linkText: 'Read more at Tripadvisor',
    platform: 'TripAdvisor',
    rating: 'TA',
    ratingBg: 'bg-green-500',
  },
  {
    name: 'Lolly',
    country: 'United Kingdom',
    flag: '🇬🇧',
    title: 'Convenience layover at KLIA',
    content:
      'Super convenient layover with shuttle bus between terminals if you need it. Very clean and staff were very well.',
    link: 'https://www.booking.com/',
    linkText: 'Read more at Booking.com',
    platform: 'Booking.com',
    rating: 'B.',
    ratingBg: 'bg-blue-600',
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    country: 'Australia',
    flag: '🇦🇺',
    title: 'Surprisingly clean, modern and convenient.. definitely will come back!',
    content:
      'Awesome location, very clean, modern, cozy and surprisingly quiet.. could not fault it really. Surprised with the amazing gym!',
    link: 'https://www.booking.com/',
    linkText: 'Read more at Booking.com',
    platform: 'Booking.com',
    rating: 'B.',
    ratingBg: 'bg-blue-600',
  },
];

const ReviewsSection = () => (
  <section className='w-full min-h-[500px] flex flex-col md:flex-row'>
    {/* Left: Heading and Nav (Desktop) / Top (Mobile) */}
    <div className='md:w-1/2 bg-[#011589] flex flex-col justify-center px-8 py-16 text-white relative'>
      <div>
        <div className='text-sm font-semibold mb-2 tracking-wider'>
          OUR REVIEWS AND RECOGNITION
        </div>
        <h2 className='text-3xl md:text-4xl font-bold mb-4 leading-tight'>
          Rest at the KLIA airport with a peace of mind
        </h2>
        <p className='text-base mb-8 opacity-90'>
          Trust not just our words, but the travellers from around the world
          who stayed with us.
        </p>
      </div>
      {/* Desktop navigation buttons */}
      <div className='hidden md:flex gap-3 mt-4'>
        <button
          className='custom-swiper-prev w-12 h-12 rounded-lg flex items-center justify-center bg-[#223a8a] text-white text-xl hover:bg-[#334ba3] transition-colors'
          aria-label='Previous Review'
        >
          <span>‹</span>
        </button>
        <button
          className='custom-swiper-next w-12 h-12 rounded-lg flex items-center justify-center bg-white text-[#011589] text-xl hover:bg-gray-100 transition-colors'
          aria-label='Next Review'
        >
          <span>›</span>
        </button>
      </div>
    </div>
    {/* Right: Swiper Carousel (Desktop) / Bottom (Mobile) */}
    <div
      className='md:w-1/2 bg-[#e5e8f6] flex items-center justify-center relative overflow-hidden min-h-[500px] px-4 py-8'
      style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
      }}
    >
      <div className='w-full max-w-6xl mx-auto'>
        <Swiper
          modules={[Pagination, Navigation]}
          slidesPerView={1}
          spaceBetween={20}
          navigation={{
            prevEl: '.custom-swiper-prev',
            nextEl: '.custom-swiper-next',
          }}
          breakpoints={{
            768: {
              slidesPerView: 2,
              spaceBetween: 30,
            },
            1024: {
              slidesPerView: 2,
              spaceBetween: 40,
            },
          }}
          className='w-full'
          style={{
            height: 'auto',
            '--swiper-slide-height': 'auto'
          } as React.CSSProperties}
        >
          {reviews.map((review, i) => (
            <SwiperSlide key={i} style={{ height: 'auto', display: 'flex' }}>
              <div className='bg-white rounded-2xl shadow-lg p-6 w-full flex flex-col justify-between min-h-[420px]'>
                {/* Header with name, flag, country and rating */}
                <div className='flex items-center justify-between mb-4'>
                  <div className='flex items-center gap-2'>
                    <span className='text-xl font-bold text-black'>
                      {review.name}
                    </span>
                    <span className='text-lg'>{review.flag}</span>
                    <span className='text-gray-600 text-sm'>
                      {review.country}
                    </span>
                  </div>
                  <div className={`${review.ratingBg} text-white px-3 py-1 rounded-lg font-bold text-lg`}>
                    {review.rating}
                  </div>
                </div>

                {/* Review title */}
                <h3 className='text-lg font-bold text-black mb-4 leading-tight'>
                  {review.title}
                </h3>

                {/* Review content */}
                <p className='text-gray-700 text-sm leading-relaxed mb-6 flex-grow'>
                  {review.content}
                </p>

                {/* Link */}
                <a
                  href={review.link}
                  className='text-[#223a8a] underline text-sm font-medium hover:text-[#011589] transition-colors'
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  {review.linkText}
                </a>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        {/* Mobile navigation buttons */}
        <div className='flex md:hidden justify-center gap-3 mt-8'>
          <button
            className='custom-swiper-prev w-12 h-12 rounded-lg flex items-center justify-center bg-[#223a8a] text-white text-xl hover:bg-[#334ba3] transition-colors'
            aria-label='Previous Review'
          >
            <span>‹</span>
          </button>
          <button
            className='custom-swiper-next w-12 h-12 rounded-lg flex items-center justify-center bg-[#011589] text-white text-xl hover:bg-[#223a8a] transition-colors'
            aria-label='Next Review'
          >
            <span>›</span>
          </button>
        </div>
      </div>
    </div>
  </section>
);

export default ReviewsSection; 