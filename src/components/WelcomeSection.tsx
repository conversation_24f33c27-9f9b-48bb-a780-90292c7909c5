import React from 'react';

const WelcomeSection = () => (
  <section className='w-full bg-white py-40 md:py-40 px-4 mt-32 md:mt-32'>
    <div className='max-w-6xl mx-auto flex flex-col items-center'>
      <h2 className='text-3xl sm:text-4xl md:text-5xl font-bold text-center mb-4 md:mb-6 text-gray-900'>
        Welcome to CapsuleTransit
      </h2>
      <p className='text-base md:text-lg text-gray-700 text-center max-w-2xl mb-8 md:mb-12 leading-relaxed px-2'>
        This is the official website of CapsuleTransit, a capsule hotel at{' '}
        <span className='font-bold text-[#011589]'>
          Kuala Lumpur International Airport (KLIA)
        </span>
        , built for travellers and flight passengers in Malaysia.
      </p>
      {/* Stats Row */}
      <div className='w-full flex flex-col sm:flex-row flex-wrap justify-center items-center gap-6 sm:gap-8 md:gap-0 mb-8 md:mb-12'>
        <div className='flex flex-col items-center flex-1 min-w-[200px] sm:min-w-0'>
          <span className='text-[#011589] text-lg sm:text-xl font-semibold text-center leading-tight'>
            Over 600,000
            <br />
            happy guest welcomed
          </span>
        </div>
        <div className='hidden md:flex items-center justify-center w-8'>
          <span className='block w-3 h-3 rounded-full bg-[#E5E8F6]' />
        </div>
        <div className='flex flex-col items-center flex-1 min-w-[200px] sm:min-w-0'>
          <span className='text-[#011589] text-lg sm:text-xl font-semibold text-center leading-tight'>
            Over 10 years
            <br />
            in hospitality
          </span>
        </div>
        <div className='hidden md:flex items-center justify-center w-8'>
          <span className='block w-3 h-3 rounded-full bg-[#E5E8F6]' />
        </div>
        <div className='flex flex-col items-center flex-1 min-w-[200px] sm:min-w-0'>
          <span className='text-[#011589] text-lg sm:text-xl font-semibold text-center leading-tight'>
            364 beds with
            <br />
            more coming
          </span>
        </div>
        <div className='hidden md:flex items-center justify-center w-8'>
          <span className='block w-3 h-3 rounded-full bg-[#E5E8F6]' />
        </div>
        <div className='flex flex-col items-center flex-1 min-w-[200px] sm:min-w-0'>
          <span className='text-[#011589] text-lg sm:text-xl font-semibold text-center leading-tight'>
            Open 24/7,
            <br />
            365. All day,
            <br />
            every day
          </span>
        </div>
      </div>
      {/* Awards Row */}
      <div className='w-full flex flex-col sm:flex-row flex-wrap justify-center items-center gap-8 sm:gap-6 md:gap-8 lg:gap-12'>
        <div className='flex flex-col items-center flex-1 min-w-[140px] max-w-[180px]'>
          <div className='h-16 w-16 sm:h-20 sm:w-20 mb-3 flex items-center justify-center'>
            <div className='w-full h-full rounded-full bg-blue-600 flex items-center justify-center'>
              <span className='text-white text-xs font-bold'>TRAVELOKA</span>
            </div>
          </div>
          <span className='text-gray-500 text-xs sm:text-sm text-center leading-tight'>
            Traveloka Hotel Awards
            <br />
            2019
          </span>
        </div>
        <div className='flex flex-col items-center flex-1 min-w-[140px] max-w-[180px]'>
          <div className='h-16 w-16 sm:h-20 sm:w-20 mb-3 flex items-center justify-center'>
            <div className='w-full h-full rounded-full bg-yellow-500 flex items-center justify-center'>
              <span className='text-white text-xs font-bold'>GOLD</span>
            </div>
          </div>
          <span className='text-gray-500 text-xs sm:text-sm text-center leading-tight'>
            2016 Agoda's Gold
            <br />
            Circle Awards
          </span>
        </div>
        <div className='flex flex-col items-center flex-1 min-w-[140px] max-w-[180px]'>
          <div className='h-16 w-16 sm:h-20 sm:w-20 mb-3 flex items-center justify-center'>
            <div className='w-full h-full rounded-full bg-green-600 flex items-center justify-center'>
              <span className='text-white text-xs font-bold'>LEED</span>
            </div>
          </div>
          <span className='text-gray-500 text-xs sm:text-sm text-center leading-tight'>
            LEED Silver Certification in
            <br />
            Green Building
          </span>
        </div>
        <div className='flex flex-col items-center flex-1 min-w-[140px] max-w-[180px]'>
          <div className='h-16 w-16 sm:h-20 sm:w-20 mb-3 flex items-center justify-center'>
            <div className='w-full h-full rounded bg-gray-700 flex items-center justify-center'>
              <span className='text-white text-xs font-bold'>NQA</span>
            </div>
          </div>
          <span className='text-gray-500 text-xs sm:text-sm text-center leading-tight'>
            NQA Certified Quality
            <br />
            Management
          </span>
        </div>
      </div>
    </div>
  </section>
);

export default WelcomeSection; 